package com.lisong;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 搜索功能集成测试
 */
@SpringBootTest
@AutoConfigureWebMvc
public class SearchIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper = new ObjectMapper();

    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void testGlobalSearchWithChineseKeyword() throws Exception {
        setup();
        
        MvcResult result = mockMvc.perform(get("/search")
                        .param("q", "阿")
                        .param("type", "all")
                        .param("page", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andReturn();

        String content = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(content);
        
        // 验证返回的数据结构
        assertTrue(jsonNode.has("data"));
        JsonNode data = jsonNode.get("data");
        assertTrue(data.has("movies"));
        assertTrue(data.has("tvShows"));
        assertTrue(data.has("varietyShows"));
        assertTrue(data.has("pagination"));
        
        // 验证分页信息
        JsonNode pagination = data.get("pagination");
        assertEquals(1, pagination.get("page").asInt());
        assertEquals(10, pagination.get("limit").asInt());
        assertTrue(pagination.get("total").asInt() >= 0);
    }

    @Test
    public void testMovieOnlySearch() throws Exception {
        setup();
        
        mockMvc.perform(get("/search")
                        .param("q", "阿")
                        .param("type", "movie")
                        .param("page", "1")
                        .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.tvShows").isEmpty())
                .andExpect(jsonPath("$.data.varietyShows").isEmpty());
    }

    @Test
    public void testTvSeriesOnlySearch() throws Exception {
        setup();
        
        mockMvc.perform(get("/search")
                        .param("q", "狂")
                        .param("type", "tv")
                        .param("page", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.movies").isEmpty())
                .andExpect(jsonPath("$.data.varietyShows").isEmpty());
    }

    @Test
    public void testVarietyShowOnlySearch() throws Exception {
        setup();
        
        mockMvc.perform(get("/search")
                        .param("q", "向往")
                        .param("type", "variety")
                        .param("page", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.movies").isEmpty())
                .andExpect(jsonPath("$.data.tvShows").isEmpty());
    }

    @Test
    public void testSearchSuggestions() throws Exception {
        setup();
        
        mockMvc.perform(get("/search/suggestions")
                        .param("q", "阿")
                        .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.suggestions").isArray());
    }

    @Test
    public void testEmptyKeywordError() throws Exception {
        setup();
        
        mockMvc.perform(get("/search")
                        .param("q", "")
                        .param("type", "all"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("搜索关键词不能为空"));
    }

    @Test
    public void testInvalidSearchType() throws Exception {
        setup();
        
        // 无效的搜索类型应该被自动设置为"all"
        mockMvc.perform(get("/search")
                        .param("q", "阿")
                        .param("type", "invalid"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    public void testPagination() throws Exception {
        setup();
        
        MvcResult result = mockMvc.perform(get("/search")
                        .param("q", "阿")
                        .param("type", "movie")
                        .param("page", "1")
                        .param("limit", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        String content = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(content);
        JsonNode pagination = jsonNode.get("data").get("pagination");
        
        assertEquals(1, pagination.get("page").asInt());
        assertEquals(2, pagination.get("limit").asInt());
    }

    @Test
    public void testNoResultsFound() throws Exception {
        setup();
        
        mockMvc.perform(get("/search")
                        .param("q", "xyz123notfound")
                        .param("type", "all"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.movies").isEmpty())
                .andExpect(jsonPath("$.data.tvShows").isEmpty())
                .andExpect(jsonPath("$.data.varietyShows").isEmpty())
                .andExpect(jsonPath("$.data.pagination.total").value(0));
    }
}
