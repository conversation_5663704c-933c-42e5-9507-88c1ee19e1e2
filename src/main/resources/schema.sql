-- Carousel table
CREATE TABLE IF NOT EXISTS carousel (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(500) NOT NULL,
    url VARCHAR(500),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Movie table
CREATE TABLE IF NOT EXISTS movie (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    original_title VARCHAR(255),
    poster VARCHAR(500),
    backdrop VARCHAR(500),
    rating DECIMAL(3,1) DEFAULT 0.0,
    movie_year INT,
    duration INT,
    genre VARCHAR(500),
    region VARCHAR(50),
    director <PERSON><PERSON><PERSON><PERSON>(255),
    "cast" TEXT,
    description TEXT,
    release_date DATE,
    box_office VARCHAR(100),
    trailer_url VARCHAR(500),
    play_url VARCHAR(500),
    movie_status VARCHAR(50) DEFAULT 'released',
    is_hot TINYINT DEFAULT 0,
    is_latest TINYINT DEFAULT 0,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- TV Series table
CREATE TABLE IF NOT EXISTS tv_series (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    poster VARCHAR(500),
    backdrop VARCHAR(500),
    rating DECIMAL(3,1) DEFAULT 0.0,
    series_year INT,
    genre VARCHAR(500), -- JSON格式存储类型数组
    region VARCHAR(50),
    director VARCHAR(255),
    "cast" TEXT, -- JSON格式存储演员数组
    description TEXT,
    series_status VARCHAR(50) DEFAULT 'ongoing', -- ongoing, completed, upcoming
    current_episode INT DEFAULT 1,
    total_episodes INT,
    update_day VARCHAR(20), -- 更新日期，如"周五"
    update_time VARCHAR(10), -- 更新时间，如"20:00"
    first_air_date DATE,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- TV Series Episode table
CREATE TABLE IF NOT EXISTS tv_series_episode (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    series_id BIGINT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    duration INT, -- 时长（分钟）
    play_url VARCHAR(500),
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES tv_series(id) ON DELETE CASCADE
);

-- Variety Show table
CREATE TABLE IF NOT EXISTS variety_show (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    poster VARCHAR(500),
    backdrop VARCHAR(500),
    rating DECIMAL(3,1) DEFAULT 0.0,
    show_year INT,
    genre VARCHAR(500), -- JSON格式存储类型数组
    region VARCHAR(50),
    description TEXT,
    show_status VARCHAR(50) DEFAULT 'weekly', -- weekly, finished, special
    update_day VARCHAR(20), -- 更新日期，如"周五"
    update_time VARCHAR(10), -- 更新时间，如"22:00"
    season INT DEFAULT 1,
    hosts TEXT, -- JSON格式存储主持人数组
    guests TEXT, -- JSON格式存储常驻嘉宾数组
    first_air_date DATE,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Variety Show Episode table
CREATE TABLE IF NOT EXISTS variety_show_episode (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    show_id BIGINT NOT NULL,
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    air_date DATE,
    duration INT, -- 时长（分钟）
    guests TEXT, -- JSON格式存储本期嘉宾数组
    play_url VARCHAR(500),
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (show_id) REFERENCES variety_show(id) ON DELETE CASCADE
);

-- User table
CREATE TABLE IF NOT EXISTS "user" (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    nickname VARCHAR(100),
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    gender VARCHAR(10), -- male, female, other
    avatar VARCHAR(500),
    birth_date DATE,
    join_date DATE DEFAULT CURRENT_DATE,
    watch_history BIGINT DEFAULT 0,
    favorites_count BIGINT DEFAULT 0,
    vip_level VARCHAR(20) DEFAULT 'regular', -- regular, premium, vip
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Role table
CREATE TABLE IF NOT EXISTS role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Role table
CREATE TABLE IF NOT EXISTS user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_id)
);

-- Favorite table
CREATE TABLE IF NOT EXISTS favorite (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    content_id BIGINT NOT NULL,
    content_type VARCHAR(20) NOT NULL, -- movie, tv_series, variety_show
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_content (user_id, content_id, content_type)
);

-- JWT Token Blacklist table (for logout functionality)
CREATE TABLE IF NOT EXISTS jwt_blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    token VARCHAR(500) NOT NULL UNIQUE,
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Movie Category table
CREATE TABLE IF NOT EXISTS movie_category (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(500),
    badge VARCHAR(50),
    sort_order INT DEFAULT 0,
    status TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
