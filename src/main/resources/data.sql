-- 插入轮播图数据
INSERT INTO carousel (title, description, image, url, sort_order, status) VALUES
('阿凡达：水之道', '詹姆斯·卡梅隆执导的科幻史诗巨作', 'https://example.com/avatar2.jpg', '/movie/1', 1, 1),
('流浪地球2', '中国科幻电影的新高度', 'https://example.com/wandering-earth2.jpg', '/movie/2', 2, 1),
('满江红', '张艺谋执导的古装悬疑片', 'https://example.com/full-river-red.jpg', '/movie/3', 3, 1),
('深海', '田晓鹏执导的动画电影', 'https://example.com/deep-sea.jpg', '/movie/4', 4, 1),
('中国乒乓之绝地反击', '体育励志电影', 'https://example.com/ping-pong.jpg', '/movie/5', 5, 1);

-- 插入电影数据
INSERT INTO movie (title, original_title, poster, backdrop, rating, movie_year, duration, genre, region, director, "cast", description, release_date, box_office, trailer_url, play_url, movie_status, is_hot, is_latest, view_count, like_count, status) VALUES
('阿凡达：水之道', 'Avatar: The Way of Water', 'https://example.com/poster1.jpg', 'https://example.com/backdrop1.jpg', 8.5, 2022, 192, '["科幻", "冒险"]', 'us', '詹姆斯·卡梅隆', '["萨姆·沃辛顿", "佐伊·索尔达娜"]', '科幻史诗巨作，讲述了杰克·萨利一家在潘多拉星球上的新冒险', '2022-12-16', '22.8亿美元', 'https://example.com/trailer1.mp4', 'https://example.com/play1.m3u8', 'released', 1, 1, 1250000, 85000, 1),
('流浪地球2', 'The Wandering Earth II', 'https://example.com/poster2.jpg', 'https://example.com/backdrop2.jpg', 8.3, 2023, 173, '["科幻", "灾难"]', 'cn', '郭帆', '["吴京", "易烊千玺", "李雪健"]', '人类为拯救地球，启动流浪地球计划的前传故事', '2023-01-22', '40.3亿人民币', 'https://example.com/trailer2.mp4', 'https://example.com/play2.m3u8', 'released', 1, 1, 980000, 72000, 1),
('满江红', 'Full River Red', 'https://example.com/poster3.jpg', 'https://example.com/backdrop3.jpg', 7.8, 2023, 159, '["古装", "悬疑"]', 'cn', '张艺谋', '["沈腾", "易烊千玺", "张译"]', '南宋绍兴年间，岳飞死后四年，一场围绕秦桧的暗杀行动', '2023-01-22', '45.4亿人民币', 'https://example.com/trailer3.mp4', 'https://example.com/play3.m3u8', 'released', 1, 1, 1100000, 68000, 1),
('复仇者联盟5：康之王朝', 'Avengers: The Kang Dynasty', 'https://example.com/poster11.jpg', 'https://example.com/backdrop11.jpg', 8.8, 2024, 180, '["动作", "科幻", "冒险"]', 'us', '德斯汀·丹尼尔·克雷顿', '["布丽·拉尔森", "安东尼·麦凯", "塞巴斯蒂安·斯坦"]', '超级英雄们面临史上最强大的敌人康征服者', '2024-05-01', '预计30亿美元', 'https://example.com/trailer11.mp4', 'https://example.com/play11.m3u8', 'upcoming', 0, 1, 0, 0, 1),
('深海', 'Deep Sea', 'https://example.com/poster4.jpg', 'https://example.com/backdrop4.jpg', 7.2, 2023, 112, '["动画", "奇幻"]', 'cn', '田晓鹏', '["苏鑫", "王亭文"]', '一个现代女孩在梦境中的奇幻冒险', '2023-01-22', '9.2亿人民币', 'https://example.com/trailer4.mp4', 'https://example.com/play4.m3u8', 'released', 0, 1, 450000, 32000, 1),
('中国乒乓之绝地反击', 'Ping Pong: The Triumph', 'https://example.com/poster5.jpg', 'https://example.com/backdrop5.jpg', 7.5, 2023, 140, '["体育", "励志"]', 'cn', '邓超,俞白眉', '["邓超", "孙俪", "许魏洲"]', '讲述中国乒乓球队在低谷期的奋斗历程', '2023-02-17', '3.8亿人民币', 'https://example.com/trailer5.mp4', 'https://example.com/play5.m3u8', 'released', 0, 1, 380000, 28000, 1),
('黑豹2：瓦坎达万岁', 'Black Panther: Wakanda Forever', 'https://example.com/poster6.jpg', 'https://example.com/backdrop6.jpg', 7.9, 2022, 161, '["动作", "科幻"]', 'us', '瑞恩·库格勒', '["安吉拉·贝塞特", "利蒂希娅·赖特"]', '瓦坎达王国面临新的挑战', '2022-11-11', '8.5亿美元', 'https://example.com/trailer6.mp4', 'https://example.com/play6.m3u8', 'released', 1, 0, 890000, 54000, 1),
('壮志凌云2：独行侠', 'Top Gun: Maverick', 'https://example.com/poster7.jpg', 'https://example.com/backdrop7.jpg', 8.7, 2022, 130, '["动作", "剧情"]', 'us', '约瑟夫·科辛斯基', '["汤姆·克鲁斯", "迈尔斯·特勒"]', '汤姆·克鲁斯回归经典角色', '2022-05-27', '14.9亿美元', 'https://example.com/trailer7.mp4', 'https://example.com/play7.m3u8', 'released', 1, 0, 1350000, 95000, 1),
('小黄人大眼萌：神偷奶爸前传', 'Minions: The Rise of Gru', 'https://example.com/poster8.jpg', 'https://example.com/backdrop8.jpg', 7.4, 2022, 87, '["动画", "喜剧"]', 'us', '凯尔·巴尔达', '["史蒂夫·卡瑞尔", "皮埃尔·柯芬"]', '小黄人的起源故事', '2022-07-01', '9.4亿美元', 'https://example.com/trailer8.mp4', 'https://example.com/play8.m3u8', 'released', 0, 0, 720000, 48000, 1),
('雷神4：爱与雷霆', 'Thor: Love and Thunder', 'https://example.com/poster9.jpg', 'https://example.com/backdrop9.jpg', 7.1, 2022, 119, '["动作", "科幻"]', 'us', '塔伊加·维迪提', '["克里斯·海姆斯沃斯", "娜塔莉·波特曼"]', '雷神的新冒险', '2022-07-08', '7.6亿美元', 'https://example.com/trailer9.mp4', 'https://example.com/play9.m3u8', 'released', 0, 0, 650000, 41000, 1),
('奇异博士2：疯狂多元宇宙', 'Doctor Strange in the Multiverse of Madness', 'https://example.com/poster10.jpg', 'https://example.com/backdrop10.jpg', 7.6, 2022, 126, '["动作", "科幻"]', 'us', '山姆·雷米', '["本尼迪克特·康伯巴奇", "伊丽莎白·奥尔森"]', '多元宇宙的疯狂冒险', '2022-05-06', '9.6亿美元', 'https://example.com/trailer10.mp4', 'https://example.com/play10.m3u8', 'released', 0, 0, 780000, 52000, 1);

-- 插入电视剧数据
INSERT INTO tv_series (title, poster, backdrop, rating, series_year, genre, region, director, "cast", description, series_status, current_episode, total_episodes, update_day, update_time, first_air_date, view_count, like_count, status) VALUES
('狂飙', 'https://example.com/tv1.jpg', 'https://example.com/tv1_bg.jpg', 9.1, 2023, '["剧情", "犯罪", "悬疑"]', 'cn', '徐纪周', '["张译", "张颂文", "李一桐"]', '一部反映扫黑除恶的现实主义力作，讲述了安欣等一线民警与黑恶势力斗争的故事', 'completed', 39, 39, '周五', '20:00', '2023-01-14', 2500000, 180000, 1),
('三体', 'https://example.com/tv2.jpg', 'https://example.com/tv2_bg.jpg', 8.7, 2023, '["科幻", "剧情", "悬疑"]', 'cn', '杨磊', '["张鲁一", "于和伟", "陈瑾"]', '根据刘慈欣同名科幻小说改编，讲述了地球文明和三体文明的信息交流', 'completed', 30, 30, '周二', '19:30', '2023-01-15', 1800000, 120000, 1),
('去有风的地方', 'https://example.com/tv3.jpg', 'https://example.com/tv3_bg.jpg', 8.2, 2023, '["都市", "治愈", "爱情"]', 'cn', '丁梓光', '["刘亦菲", "李现"]', '讲述了都市女性许红豆因为闺蜜去世而陷入人生低谷，前往大理云苗村治愈自己的故事', 'completed', 40, 40, '周三', '20:00', '2023-01-03', 1600000, 95000, 1),
('梦华录', 'https://example.com/tv4.jpg', 'https://example.com/tv4_bg.jpg', 8.8, 2022, '["古装", "爱情", "剧情"]', 'cn', '杨阳', '["刘亦菲", "陈晓", "柳岩"]', '根据关汉卿元曲改编，讲述了赵盼儿、宋引章、孙三娘三个女性在汴京奋斗的故事', 'completed', 40, 40, '周一', '20:00', '2022-06-02', 2200000, 150000, 1),
('人世间', 'https://example.com/tv5.jpg', 'https://example.com/tv5_bg.jpg', 9.2, 2022, '["年代", "剧情", "家庭"]', 'cn', '李路', '["雷佳音", "辛柏青", "宋佳"]', '根据梁晓声同名小说改编，以周家三兄妹的人生轨迹为主线，展现了近50年来中国社会的巨大变迁', 'completed', 58, 58, '周日', '19:30', '2022-01-28', 3000000, 220000, 1),
('庆余年2', 'https://example.com/tv6.jpg', 'https://example.com/tv6_bg.jpg', 8.5, 2024, '["古装", "喜剧", "权谋"]', 'cn', '孙皓', '["张若昀", "李沁", "陈道明"]', '范闲继续在庆国朝堂与江湖中游走，面对更加复杂的政治斗争', 'ongoing', 25, 36, '周二', '20:00', '2024-05-16', 1200000, 85000, 1),
('繁花', 'https://example.com/tv7.jpg', 'https://example.com/tv7_bg.jpg', 8.9, 2023, '["年代", "剧情", "商战"]', 'cn', '王家卫', '["胡歌", "马伊琍", "唐嫣"]', '根据金宇澄同名小说改编，讲述了90年代上海滩的商业传奇故事', 'completed', 30, 30, '周四', '19:30', '2023-12-27', 1900000, 130000, 1),
('长月烬明', 'https://example.com/tv8.jpg', 'https://example.com/tv8_bg.jpg', 8.3, 2023, '["古装", "仙侠", "爱情"]', 'cn', '居然', '["罗云熙", "白鹿"]', '讲述了黎苏苏为了拯救师父澹台烬，不断回到过去改变历史的仙侠爱情故事', 'completed', 40, 40, '周一', '20:00', '2023-04-06', 1500000, 110000, 1),
('以爱为营', 'https://example.com/tv9.jpg', 'https://example.com/tv9_bg.jpg', 7.8, 2023, '["都市", "爱情", "职场"]', 'cn', '钟澍佳', '["白鹿", "王鹤棣"]', '讲述了娱乐记者郑书意和金融才子时宴之间的都市爱情故事', 'completed', 35, 35, '周三', '20:00', '2023-07-20', 1100000, 75000, 1),
('莲花楼', 'https://example.com/tv10.jpg', 'https://example.com/tv10_bg.jpg', 8.6, 2023, '["古装", "武侠", "悬疑"]', 'cn', '郭虎', '["成毅", "曾舜晞", "肖顺尧"]', '讲述了李莲花、方多病、笛飞声三人携手破案，在江湖中寻找真相的故事', 'completed', 40, 40, '周二', '19:30', '2023-07-27', 1400000, 95000, 1);

-- 插入电视剧剧集数据
INSERT INTO tv_series_episode (series_id, episode_number, title, duration, play_url, status) VALUES
-- 狂飙剧集
(1, 1, '第1集', 45, 'https://example.com/tv1_ep1.m3u8', 1),
(1, 2, '第2集', 45, 'https://example.com/tv1_ep2.m3u8', 1),
(1, 3, '第3集', 45, 'https://example.com/tv1_ep3.m3u8', 1),
-- 三体剧集
(2, 1, '第1集', 50, 'https://example.com/tv2_ep1.m3u8', 1),
(2, 2, '第2集', 50, 'https://example.com/tv2_ep2.m3u8', 1),
(2, 3, '第3集', 50, 'https://example.com/tv2_ep3.m3u8', 1),
-- 去有风的地方剧集
(3, 1, '第1集', 42, 'https://example.com/tv3_ep1.m3u8', 1),
(3, 2, '第2集', 42, 'https://example.com/tv3_ep2.m3u8', 1),
(3, 3, '第3集', 42, 'https://example.com/tv3_ep3.m3u8', 1),
-- 梦华录剧集
(4, 1, '第1集', 48, 'https://example.com/tv4_ep1.m3u8', 1),
(4, 2, '第2集', 48, 'https://example.com/tv4_ep2.m3u8', 1),
(4, 3, '第3集', 48, 'https://example.com/tv4_ep3.m3u8', 1),
-- 人世间剧集
(5, 1, '第1集', 52, 'https://example.com/tv5_ep1.m3u8', 1),
(5, 2, '第2集', 52, 'https://example.com/tv5_ep2.m3u8', 1),
(5, 3, '第3集', 52, 'https://example.com/tv5_ep3.m3u8', 1);

-- 插入综艺节目数据
INSERT INTO variety_show (title, poster, backdrop, rating, show_year, genre, region, description, show_status, update_day, update_time, season, hosts, guests, first_air_date, view_count, like_count, status) VALUES
('向往的生活', 'https://example.com/variety1.jpg', 'https://example.com/variety1_bg.jpg', 9.2, 2024, '["真人秀", "生活", "治愈"]', 'cn', '明星嘉宾体验田园生活，享受慢节奏的乡村时光，在自然中寻找内心的宁静', 'weekly', '周五', '22:00', 8, '["黄磊", "何炅"]', '["张艺兴", "彭昱畅"]', '2024-01-01', 3500000, 250000, 1),
('奔跑吧', 'https://example.com/variety2.jpg', 'https://example.com/variety2_bg.jpg', 8.8, 2024, '["真人秀", "竞技", "游戏"]', 'cn', '明星嘉宾通过各种游戏和挑战，展现团队合作精神和个人魅力', 'weekly', '周五', '20:20', 12, '["李晨", "杨颖"]', '["郑恺", "沙溢", "白鹿", "周深"]', '2024-01-05', 3200000, 220000, 1),
('中国好声音', 'https://example.com/variety3.jpg', 'https://example.com/variety3_bg.jpg', 8.5, 2024, '["音乐", "选秀", "竞技"]', 'cn', '音乐选秀节目，发掘和培养优秀的音乐人才，为观众带来高质量的音乐享受', 'weekly', '周五', '21:00', 13, '["华少"]', '["周杰伦", "李荣浩", "那英", "庾澄庆"]', '2024-07-19', 2800000, 180000, 1),
('快乐大本营', 'https://example.com/variety4.jpg', 'https://example.com/variety4_bg.jpg', 8.3, 2024, '["娱乐", "访谈", "游戏"]', 'cn', '经典综艺节目，以轻松愉快的方式与明星嘉宾互动，为观众带来欢声笑语', 'finished', '周六', '20:00', 25, '["何炅", "谢娜"]', '["维嘉", "杜海涛", "吴昕"]', '2024-01-06', 2500000, 160000, 1),
('极限挑战', 'https://example.com/variety5.jpg', 'https://example.com/variety5_bg.jpg', 8.1, 2024, '["真人秀", "冒险", "挑战"]', 'cn', '明星嘉宾接受各种极限挑战，在困难中展现真实的自己和团队精神', 'weekly', '周日', '21:00', 10, '["黄磊"]', '["罗志祥", "张艺兴", "王迅", "贾乃亮"]', '2024-05-12', 2200000, 140000, 1),
('乘风破浪的姐姐', 'https://example.com/variety6.jpg', 'https://example.com/variety6_bg.jpg', 8.9, 2024, '["音乐", "竞技", "真人秀"]', 'cn', '30+女艺人通过音乐竞演，展现成熟女性的魅力和实力', 'finished', '周五', '20:00', 5, '["黄晓明"]', '["宁静", "张柏芝", "陈妍希", "容祖儿"]', '2024-05-17', 4200000, 320000, 1),
('明星大侦探', 'https://example.com/variety7.jpg', 'https://example.com/variety7_bg.jpg', 9.0, 2024, '["推理", "悬疑", "真人秀"]', 'cn', '明星嘉宾化身侦探，通过推理和分析破解各种悬疑案件', 'weekly', '周三', '12:00', 9, '["何炅"]', '["撒贝宁", "白敬亭", "刘昊然", "张若昀"]', '2024-01-10', 3800000, 280000, 1),
('这就是街舞', 'https://example.com/variety8.jpg', 'https://example.com/variety8_bg.jpg', 8.7, 2024, '["舞蹈", "竞技", "音乐"]', 'cn', '街舞选手通过精彩的舞蹈表演和激烈的竞争，展现街舞文化的魅力', 'finished', '周六', '20:00', 6, '["韩庚"]', '["易烊千玺", "刘宪华", "布布", "冯正"]', '2024-08-24', 2600000, 170000, 1),
('中餐厅', 'https://example.com/variety9.jpg', 'https://example.com/variety9_bg.jpg', 8.4, 2024, '["生活", "美食", "真人秀"]', 'cn', '明星嘉宾经营中餐厅，在异国他乡传播中华美食文化', 'finished', '周五', '22:00', 8, '["黄晓明"]', '["周也", "姚安娜", "尹正", "檀健次"]', '2024-07-05', 2400000, 150000, 1),
('青春有你', 'https://example.com/variety10.jpg', 'https://example.com/variety10_bg.jpg', 8.2, 2024, '["选秀", "音乐", "舞蹈"]', 'cn', '年轻练习生通过训练和竞演，追求成为偶像的梦想', 'special', '周六', '20:00', 4, '["李荣浩"]', '["蔡徐坤", "陈立农", "范丞丞", "黄明昊"]', '2024-03-21', 3100000, 200000, 1),
('密室大逃脱', 'https://example.com/variety11.jpg', 'https://example.com/variety11_bg.jpg', 8.6, 2024, '["推理", "冒险", "真人秀"]', 'cn', '明星嘉宾在各种主题密室中解谜逃脱，体验惊险刺激的冒险之旅', 'weekly', '周六', '22:00', 6, '["何炅"]', '["谢娜", "张国伟", "大张伟", "杨幂"]', '2024-04-13', 2900000, 190000, 1),
('王牌对王牌', 'https://example.com/variety12.jpg', 'https://example.com/variety12_bg.jpg', 8.5, 2024, '["游戏", "娱乐", "竞技"]', 'cn', '两队明星嘉宾通过各种游戏对战，在欢声笑语中展现才艺和默契', 'weekly', '周五', '20:20', 9, '["沈腾"]', '["贾玲", "华晨宇", "关晓彤"]', '2024-02-09', 3300000, 230000, 1);

-- 插入综艺节目期数数据
INSERT INTO variety_show_episode (show_id, episode_number, title, air_date, duration, guests, play_url, status) VALUES
-- 向往的生活期数
(1, 1, '第1期：新年第一餐', '2024-01-05', 90, '["刘昊然", "关晓彤"]', 'https://example.com/variety1_ep1.m3u8', 1),
(1, 2, '第2期：田园诗意', '2024-01-12', 90, '["易烊千玺", "张子枫"]', 'https://example.com/variety1_ep2.m3u8', 1),
(1, 3, '第3期：春耕时节', '2024-01-19', 90, '["王俊凯", "欧阳娜娜"]', 'https://example.com/variety1_ep3.m3u8', 1),
-- 奔跑吧期数
(2, 1, '第1期：新年开跑', '2024-01-05', 100, '["迪丽热巴", "邓超"]', 'https://example.com/variety2_ep1.m3u8', 1),
(2, 2, '第2期：团队挑战', '2024-01-12', 100, '["杨紫", "肖战"]', 'https://example.com/variety2_ep2.m3u8', 1),
(2, 3, '第3期：城市探索', '2024-01-19', 100, '["赵丽颖", "王一博"]', 'https://example.com/variety2_ep3.m3u8', 1),
-- 中国好声音期数
(3, 1, '第1期：盲选开始', '2024-07-19', 120, '["毛不易", "周深"]', 'https://example.com/variety3_ep1.m3u8', 1),
(3, 2, '第2期：导师争夺', '2024-07-26', 120, '["张碧晨", "汪苏泷"]', 'https://example.com/variety3_ep2.m3u8', 1),
(3, 3, '第3期：实力对决', '2024-08-02', 120, '["袁娅维", "胡彦斌"]', 'https://example.com/variety3_ep3.m3u8', 1),
-- 明星大侦探期数
(7, 1, '第1期：古宅疑云', '2024-01-10', 110, '["杨紫", "魏大勋"]', 'https://example.com/variety7_ep1.m3u8', 1),
(7, 2, '第2期：密室逃脱', '2024-01-17', 110, '["迪丽热巴", "邓伦"]', 'https://example.com/variety7_ep2.m3u8', 1),
(7, 3, '第3期：时空穿越', '2024-01-24', 110, '["杨幂", "黄明昊"]', 'https://example.com/variety7_ep3.m3u8', 1),
-- 王牌对王牌期数
(12, 1, '第1期：新春特辑', '2024-02-09', 95, '["宋茜", "王嘉尔"]', 'https://example.com/variety12_ep1.m3u8', 1),
(12, 2, '第2期：经典重现', '2024-02-16', 95, '["杨紫", "任嘉伦"]', 'https://example.com/variety12_ep2.m3u8', 1),
(12, 3, '第3期：游戏大战', '2024-02-23', 95, '["赵露思", "丁禹兮"]', 'https://example.com/variety12_ep3.m3u8', 1);

-- 插入角色数据
INSERT INTO role (name, description, status) VALUES
('ADMIN', '系统管理员，拥有所有权限', 1),
('USER', '普通用户，拥有基本权限', 1);

-- 插入用户数据（密码都是 password123，已加密）
INSERT INTO "user" (username, nickname, password, email, phone, gender, avatar, birth_date, join_date, watch_history, favorites_count, vip_level, status) VALUES
('admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138000', 'male', 'https://example.com/admin_avatar.jpg', '1990-01-01', '2024-01-01', 0, 0, 'premium', 1),
('user001', '电影爱好者', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138001', 'male', 'https://example.com/avatar1.jpg', '1995-03-15', '2024-01-01', 156, 23, 'premium', 1),
('user002', '追剧达人', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138002', 'female', 'https://example.com/avatar2.jpg', '1992-07-20', '2024-01-02', 289, 45, 'vip', 1),
('user003', '综艺迷', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138003', 'female', 'https://example.com/avatar3.jpg', '1998-11-08', '2024-01-03', 78, 12, 'regular', 1),
('user004', '科幻粉', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138004', 'male', 'https://example.com/avatar4.jpg', '1988-05-12', '2024-01-04', 234, 67, 'premium', 1),
('user005', '动作片控', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P2.nHb8GjU5cWO', '<EMAIL>', '13800138005', 'other', 'https://example.com/avatar5.jpg', '1993-09-25', '2024-01-05', 145, 34, 'regular', 1);

-- 插入用户角色关联数据
INSERT INTO user_role (user_id, role_id) VALUES
(1, 1), -- admin 用户拥有管理员角色
(1, 2), -- admin 用户同时拥有普通用户角色
(2, 2), -- user001 拥有普通用户角色
(3, 2), -- user002 拥有普通用户角色
(4, 2), -- user003 拥有普通用户角色
(5, 2), -- user004 拥有普通用户角色
(6, 2); -- user005 拥有普通用户角色

-- 插入收藏数据示例
INSERT INTO favorite (user_id, content_id, content_type) VALUES
(2, 1, 'movie'),    -- user001 收藏了阿凡达：水之道
(2, 2, 'movie'),    -- user001 收藏了流浪地球2
(2, 1, 'tv_series'), -- user001 收藏了狂飙
(3, 3, 'movie'),    -- user002 收藏了满江红
(3, 2, 'tv_series'), -- user002 收藏了三体
(3, 1, 'variety_show'), -- user002 收藏了向往的生活
(4, 1, 'movie'),    -- user003 收藏了阿凡达：水之道
(4, 4, 'movie'),    -- user003 收藏了深海
(5, 6, 'movie'),    -- user004 收藏了黑豹2
(5, 7, 'movie'),    -- user004 收藏了壮志凌云2
(6, 8, 'movie'),    -- user005 收藏了小黄人大眼萌
(6, 3, 'tv_series'); -- user005 收藏了去有风的地方

-- 添加更多电影数据用于搜索测试
INSERT INTO movie (title, original_title, poster, backdrop, rating, movie_year, duration, genre, region, director, "cast", description, release_date, box_office, trailer_url, play_url, movie_status, is_hot, is_latest, view_count, like_count, status) VALUES
('阿拉丁', 'Aladdin', 'https://example.com/aladdin.jpg', 'https://example.com/aladdin_bg.jpg', 7.8, 2019, 128, '["奇幻", "冒险", "音乐"]', 'us', '盖·里奇', '["威尔·史密斯", "梅娜·马苏德"]', '经典迪士尼动画的真人版改编', '2019-05-24', '10.5亿美元', 'https://example.com/aladdin_trailer.mp4', 'https://example.com/aladdin_play.m3u8', 'released', 0, 0, 850000, 62000, 1),
('阿甘正传', 'Forrest Gump', 'https://example.com/forrest.jpg', 'https://example.com/forrest_bg.jpg', 9.5, 1994, 142, '["剧情", "爱情"]', 'us', '罗伯特·泽米吉斯', '["汤姆·汉克斯", "罗宾·怀特"]', '一个智商只有75的男人的传奇人生', '1994-07-06', '6.8亿美元', 'https://example.com/forrest_trailer.mp4', 'https://example.com/forrest_play.m3u8', 'released', 1, 0, 2500000, 180000, 1),
('阿基拉', 'Akira', 'https://example.com/akira.jpg', 'https://example.com/akira_bg.jpg', 8.9, 1988, 124, '["动画", "科幻", "动作"]', 'jp', '大友克洋', '["岩田光央", "佐佐木望"]', '日本经典赛博朋克动画电影', '1988-07-16', '4900万美元', 'https://example.com/akira_trailer.mp4', 'https://example.com/akira_play.m3u8', 'released', 0, 0, 650000, 45000, 1),
('蜘蛛侠：英雄无归', 'Spider-Man: No Way Home', 'https://example.com/spiderman.jpg', 'https://example.com/spiderman_bg.jpg', 8.9, 2021, 148, '["动作", "科幻", "冒险"]', 'us', '乔·沃茨', '["汤姆·赫兰德", "赞达亚"]', '三代蜘蛛侠同框的史诗级作品', '2021-12-17', '19.2亿美元', 'https://example.com/spiderman_trailer.mp4', 'https://example.com/spiderman_play.m3u8', 'released', 1, 0, 1800000, 125000, 1),
('蝙蝠侠：黑暗骑士', 'The Dark Knight', 'https://example.com/batman.jpg', 'https://example.com/batman_bg.jpg', 9.3, 2008, 152, '["动作", "犯罪", "剧情"]', 'us', '克里斯托弗·诺兰', '["克里斯蒂安·贝尔", "希斯·莱杰"]', '诺兰蝙蝠侠三部曲的巅峰之作', '2008-07-18', '10.0亿美元', 'https://example.com/batman_trailer.mp4', 'https://example.com/batman_play.m3u8', 'released', 1, 0, 2200000, 165000, 1),
('钢铁侠', 'Iron Man', 'https://example.com/ironman.jpg', 'https://example.com/ironman_bg.jpg', 8.6, 2008, 126, '["动作", "科幻", "冒险"]', 'us', '乔恩·费儒', '["小罗伯特·唐尼", "格温妮丝·帕特洛"]', '漫威电影宇宙的开山之作', '2008-05-02', '5.9亿美元', 'https://example.com/ironman_trailer.mp4', 'https://example.com/ironman_play.m3u8', 'released', 1, 0, 1950000, 140000, 1),
('美国队长', 'Captain America: The First Avenger', 'https://example.com/captain.jpg', 'https://example.com/captain_bg.jpg', 7.8, 2011, 124, '["动作", "科幻", "冒险"]', 'us', '乔·约翰斯顿', '["克里斯·埃文斯", "海莉·阿特维尔"]', '二战时期的超级英雄诞生记', '2011-07-22', '3.7亿美元', 'https://example.com/captain_trailer.mp4', 'https://example.com/captain_play.m3u8', 'released', 0, 0, 1200000, 85000, 1);

-- 添加更多电视剧数据用于搜索测试
INSERT INTO tv_series (title, poster, backdrop, rating, series_year, genre, region, director, "cast", description, series_status, current_episode, total_episodes, update_day, update_time, first_air_date, view_count, like_count, status) VALUES
('权力的游戏', 'https://example.com/got.jpg', 'https://example.com/got_bg.jpg', 9.5, 2011, '["奇幻", "剧情", "战争"]', 'us', '大卫·贝尼奥夫', '["艾米莉亚·克拉克", "基特·哈灵顿"]', '史诗级奇幻剧集，讲述七大王国的权力斗争', 'completed', 73, 73, '周日', '21:00', '2011-04-17', 5000000, 380000, 1),
('绝命毒师', 'https://example.com/breaking.jpg', 'https://example.com/breaking_bg.jpg', 9.8, 2008, '["犯罪", "剧情", "惊悚"]', 'us', '文斯·吉利根', '["布莱恩·科兰斯顿", "亚伦·保尔"]', '一个高中化学老师变成毒枭的故事', 'completed', 62, 62, '周日', '22:00', '2008-01-20', 4500000, 350000, 1),
('老友记', 'https://example.com/friends.jpg', 'https://example.com/friends_bg.jpg', 9.7, 1994, '["喜剧", "爱情", "生活"]', 'us', '大卫·克兰', '["詹妮弗·安妮斯顿", "柯特妮·考克斯"]', '六个好朋友在纽约的生活故事', 'completed', 236, 236, '周四', '20:00', '1994-09-22', 6000000, 450000, 1),
('生活大爆炸', 'https://example.com/bigbang.jpg', 'https://example.com/bigbang_bg.jpg', 9.2, 2007, '["喜剧", "科学", "生活"]', 'us', '查克·洛尔', '["吉姆·帕森斯", "约翰尼·盖尔克奇"]', '四个科学宅男的搞笑日常', 'completed', 279, 279, '周四', '20:00', '2007-09-24', 3800000, 280000, 1),
('西部世界', 'https://example.com/westworld.jpg', 'https://example.com/westworld_bg.jpg', 8.8, 2016, '["科幻", "西部", "惊悚"]', 'us', '乔纳森·诺兰', '["埃文·蕾切尔·伍德", "安东尼·霍普金斯"]', '未来主题公园中人工智能的觉醒', 'completed', 36, 36, '周日', '21:00', '2016-10-02', 2800000, 200000, 1);

-- 添加更多综艺节目数据用于搜索测试
INSERT INTO variety_show (title, poster, backdrop, rating, show_year, genre, region, description, show_status, update_day, update_time, season, hosts, guests, first_air_date, view_count, like_count, status) VALUES
('天天向上', 'https://example.com/tiantiangup.jpg', 'https://example.com/tiantiangup_bg.jpg', 8.3, 2024, '["娱乐", "访谈", "文化"]', 'cn', '以传播中华优秀传统文化为主题的综艺节目', 'weekly', '周日', '22:00', 18, '["汪涵", "钱枫"]', '["大张伟", "王一博"]', '2024-01-07', 2100000, 135000, 1),
('非诚勿扰', 'https://example.com/feicheng.jpg', 'https://example.com/feicheng_bg.jpg', 8.1, 2024, '["相亲", "情感", "真人秀"]', 'cn', '单身男女的相亲交友节目', 'weekly', '周六', '21:10', 15, '["孟非"]', '["黄菡", "乐嘉"]', '2024-01-06', 1900000, 120000, 1),
('我是歌手', 'https://example.com/singer.jpg', 'https://example.com/singer_bg.jpg', 9.1, 2024, '["音乐", "竞技", "选秀"]', 'cn', '顶级歌手的音乐竞技节目', 'finished', '周五', '20:00', 8, '["汪涵"]', '["韩红", "林志炫", "邓紫棋"]', '2024-01-12', 3600000, 270000, 1),
('爸爸去哪儿', 'https://example.com/daddy.jpg', 'https://example.com/daddy_bg.jpg', 8.9, 2024, '["亲子", "真人秀", "旅行"]', 'cn', '明星爸爸带着孩子的温馨旅程', 'finished', '周五', '22:00', 6, '["李锐"]', '["黄磊", "郭涛", "林志颖"]', '2024-10-11', 4100000, 310000, 1),
('中国达人秀', 'https://example.com/talent.jpg', 'https://example.com/talent_bg.jpg', 8.4, 2024, '["才艺", "选秀", "竞技"]', 'cn', '展现各种才艺的选秀节目', 'weekly', '周日', '21:00', 7, '["沈腾"]', '["杨幂", "金星", "蔡国庆"]', '2024-07-14', 2700000, 175000, 1);

-- 插入电影分类数据
INSERT INTO movie_category (id, name, description, image, badge, sort_order, status) VALUES
('action', '动作片', '刺激的打斗场面，紧张的追逐戏码，让观众肾上腺素飙升', 'https://example.com/action.jpg', '热门', 1, 1),
('comedy', '喜剧片', '轻松幽默的故事情节，带给观众欢声笑语', 'https://example.com/comedy.jpg', '推荐', 2, 1),
('romance', '爱情片', '浪漫温馨的爱情故事，触动人心的情感体验', 'https://example.com/romance.jpg', '', 3, 1),
('sci-fi', '科幻片', '未来科技与想象力的完美结合，探索未知世界', 'https://example.com/sci-fi.jpg', '热门', 4, 1),
('horror', '恐怖片', '惊悚刺激的恐怖氛围，挑战观众的心理极限', 'https://example.com/horror.jpg', '', 5, 1),
('drama', '剧情片', '深刻的人生故事，展现复杂的人性与情感', 'https://example.com/drama.jpg', '经典', 6, 1),
('animation', '动画片', '精美的动画制作，适合全家观看的温馨故事', 'https://example.com/animation.jpg', '家庭', 7, 1),
('thriller', '悬疑片', '扣人心弦的悬疑情节，让观众猜测到最后一刻', 'https://example.com/thriller.jpg', '', 8, 1),
('adventure', '冒险片', '刺激的冒险旅程，探索未知的神秘世界', 'https://example.com/adventure.jpg', '', 9, 1),
('fantasy', '奇幻片', '魔法与奇迹的奇幻世界，超越现实的想象力', 'https://example.com/fantasy.jpg', '', 10, 1),
('crime', '犯罪片', '复杂的犯罪情节，展现正义与邪恶的较量', 'https://example.com/crime.jpg', '', 11, 1),
('documentary', '纪录片', '真实记录生活与历史，传递知识与思考', 'https://example.com/documentary.jpg', '教育', 12, 1);
