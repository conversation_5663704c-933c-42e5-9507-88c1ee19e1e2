package com.lisong.mapper;

import com.lisong.entity.UserRole;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户角色关联数据访问接口
 */
@Mapper
public interface UserRoleMapper {
    
    /**
     * 根据用户ID查询用户角色关联
     */
    @Select("SELECT id, user_id as userId, role_id as roleId, created_at as createdAt " +
            "FROM user_role WHERE user_id = #{userId}")
    List<UserRole> findByUserId(Long userId);
    
    /**
     * 根据角色ID查询用户角色关联
     */
    @Select("SELECT id, user_id as userId, role_id as roleId, created_at as createdAt " +
            "FROM user_role WHERE role_id = #{roleId}")
    List<UserRole> findByRoleId(Long roleId);
    
    /**
     * 插入用户角色关联
     */
    @Insert("INSERT INTO user_role (user_id, role_id) VALUES (#{userId}, #{roleId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserRole userRole);
    
    /**
     * 删除用户的所有角色
     */
    @Delete("DELETE FROM user_role WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    /**
     * 删除特定的用户角色关联
     */
    @Delete("DELETE FROM user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 检查用户是否拥有特定角色
     */
    @Select("SELECT COUNT(*) FROM user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int existsByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
}
