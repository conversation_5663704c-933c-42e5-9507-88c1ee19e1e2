package com.lisong.mapper;

import com.lisong.entity.User;
import com.lisong.entity.Role;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户数据访问接口
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT id, username, nickname, password, email, phone, gender, avatar, birth_date as birthDate, " +
            "join_date as joinDate, watch_history as watchHistory, favorites_count as favoritesCount, " +
            "vip_level as vipLevel, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM \"user\" WHERE username = #{username} AND status = 1")
    User findByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT id, username, nickname, password, email, phone, gender, avatar, birth_date as birthDate, " +
            "join_date as joinDate, watch_history as watchHistory, favorites_count as favoritesCount, " +
            "vip_level as vipLevel, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM \"user\" WHERE email = #{email} AND status = 1")
    User findByEmail(String email);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT id, username, nickname, password, email, phone, gender, avatar, birth_date as birthDate, " +
            "join_date as joinDate, watch_history as watchHistory, favorites_count as favoritesCount, " +
            "vip_level as vipLevel, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM \"user\" WHERE id = #{id} AND status = 1")
    User findById(Long id);
    
    /**
     * 插入新用户
     */
    @Insert("INSERT INTO \"user\" (username, nickname, password, email, phone, gender, avatar, birth_date, " +
            "join_date, watch_history, favorites_count, vip_level, status) " +
            "VALUES (#{username}, #{nickname}, #{password}, #{email}, #{phone}, #{gender}, #{avatar}, " +
            "#{birthDate}, #{joinDate}, #{watchHistory}, #{favoritesCount}, #{vipLevel}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    /**
     * 更新用户信息
     */
    @Update("UPDATE \"user\" SET nickname = #{nickname}, email = #{email}, phone = #{phone}, " +
            "gender = #{gender}, avatar = #{avatar}, birth_date = #{birthDate}, " +
            "watch_history = #{watchHistory}, favorites_count = #{favoritesCount}, " +
            "vip_level = #{vipLevel}, updated_at = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int update(User user);
    
    /**
     * 更新用户密码
     */
    @Update("UPDATE \"user\" SET password = #{password}, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password);
    
    /**
     * 删除用户（软删除）
     */
    @Update("UPDATE \"user\" SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 获取用户的角色列表
     */
    @Select("SELECT r.id, r.name, r.description, r.status, r.created_at as createdAt, r.updated_at as updatedAt " +
            "FROM role r " +
            "INNER JOIN user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1")
    List<Role> findRolesByUserId(Long userId);
    
    /**
     * 查询所有用户（分页）
     */
    @Select("SELECT id, username, nickname, password, email, phone, gender, avatar, birth_date as birthDate, " +
            "join_date as joinDate, watch_history as watchHistory, favorites_count as favoritesCount, " +
            "vip_level as vipLevel, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM \"user\" WHERE status = 1 " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<User> findAllUsers(@Param("limit") int limit, @Param("offset") int offset);
    
    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM \"user\" WHERE status = 1")
    Long countUsers();
    
    /**
     * 根据关键词搜索用户
     */
    @Select("SELECT id, username, nickname, password, email, phone, gender, avatar, birth_date as birthDate, " +
            "join_date as joinDate, watch_history as watchHistory, favorites_count as favoritesCount, " +
            "vip_level as vipLevel, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM \"user\" WHERE status = 1 AND (username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR nickname LIKE CONCAT('%', #{keyword}, '%') OR email LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<User> searchUsers(@Param("keyword") String keyword, @Param("limit") int limit, @Param("offset") int offset);
    
    /**
     * 统计搜索结果数量
     */
    @Select("SELECT COUNT(*) FROM \"user\" WHERE status = 1 AND (username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR nickname LIKE CONCAT('%', #{keyword}, '%') OR email LIKE CONCAT('%', #{keyword}, '%'))")
    Long countSearchUsers(@Param("keyword") String keyword);
}
