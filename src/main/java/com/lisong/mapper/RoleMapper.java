package com.lisong.mapper;

import com.lisong.entity.Role;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 角色数据访问接口
 */
@Mapper
public interface RoleMapper {
    
    /**
     * 根据ID查询角色
     */
    @Select("SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM role WHERE id = #{id} AND status = 1")
    Role findById(Long id);
    
    /**
     * 根据名称查询角色
     */
    @Select("SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM role WHERE name = #{name} AND status = 1")
    Role findByName(String name);
    
    /**
     * 查询所有角色
     */
    @Select("SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM role WHERE status = 1 ORDER BY id ASC")
    List<Role> findAll();
    
    /**
     * 插入新角色
     */
    @Insert("INSERT INTO role (name, description, status) VALUES (#{name}, #{description}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Role role);
    
    /**
     * 更新角色信息
     */
    @Update("UPDATE role SET name = #{name}, description = #{description}, " +
            "updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int update(Role role);
    
    /**
     * 删除角色（软删除）
     */
    @Update("UPDATE role SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int deleteById(Long id);
}
