package com.lisong.mapper;

import com.lisong.entity.Favorite;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 收藏数据访问接口
 */
@Mapper
public interface FavoriteMapper {
    
    /**
     * 根据用户ID查询收藏列表
     */
    @Select("SELECT id, user_id as userId, content_id as contentId, content_type as contentType, " +
            "created_at as createdAt FROM favorite WHERE user_id = #{userId} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<Favorite> findByUserId(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") int offset);
    
    /**
     * 根据用户ID和内容类型查询收藏列表
     */
    @Select("SELECT id, user_id as userId, content_id as contentId, content_type as contentType, " +
            "created_at as createdAt FROM favorite WHERE user_id = #{userId} AND content_type = #{contentType} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<Favorite> findByUserIdAndContentType(@Param("userId") Long userId, @Param("contentType") String contentType, 
                                              @Param("limit") int limit, @Param("offset") int offset);
    
    /**
     * 检查是否已收藏
     */
    @Select("SELECT COUNT(*) FROM favorite WHERE user_id = #{userId} AND content_id = #{contentId} " +
            "AND content_type = #{contentType}")
    int existsByUserIdAndContentIdAndContentType(@Param("userId") Long userId, @Param("contentId") Long contentId, 
                                                 @Param("contentType") String contentType);
    
    /**
     * 添加收藏
     */
    @Insert("INSERT INTO favorite (user_id, content_id, content_type) " +
            "VALUES (#{userId}, #{contentId}, #{contentType})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Favorite favorite);
    
    /**
     * 删除收藏
     */
    @Delete("DELETE FROM favorite WHERE user_id = #{userId} AND content_id = #{contentId} " +
            "AND content_type = #{contentType}")
    int deleteByUserIdAndContentIdAndContentType(@Param("userId") Long userId, @Param("contentId") Long contentId, 
                                                 @Param("contentType") String contentType);
    
    /**
     * 根据用户ID统计收藏数量
     */
    @Select("SELECT COUNT(*) FROM favorite WHERE user_id = #{userId}")
    Long countByUserId(Long userId);
    
    /**
     * 根据用户ID和内容类型统计收藏数量
     */
    @Select("SELECT COUNT(*) FROM favorite WHERE user_id = #{userId} AND content_type = #{contentType}")
    Long countByUserIdAndContentType(@Param("userId") Long userId, @Param("contentType") String contentType);
    
    /**
     * 删除用户的所有收藏
     */
    @Delete("DELETE FROM favorite WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
}
