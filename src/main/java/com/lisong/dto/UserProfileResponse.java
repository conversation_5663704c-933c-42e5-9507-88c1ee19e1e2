package com.lisong.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDate;

/**
 * 用户信息响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileResponse {
    
    private Long userId;
    private String username;
    private String email;
    private String avatar;
    private LocalDate joinDate;
    private Long watchHistory;
    private Long favorites;
    private String vipLevel;
}
