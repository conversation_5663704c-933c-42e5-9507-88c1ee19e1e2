package com.lisong.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 收藏请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteRequest {
    
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    @NotBlank(message = "内容类型不能为空")
    private String contentType; // movie, tv_series, variety_show
    
    @NotBlank(message = "操作类型不能为空")
    private String action; // add, remove
}
