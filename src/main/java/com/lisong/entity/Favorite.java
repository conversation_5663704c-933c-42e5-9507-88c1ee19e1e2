package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * 收藏实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Favorite {
    
    private Long id;
    private Long userId;
    private Long contentId;
    private String contentType; // movie, tv_series, variety_show
    private LocalDateTime createdAt;
}
