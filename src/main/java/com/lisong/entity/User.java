package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    
    private Long id;
    private String username;
    private String nickname;
    private String password;
    private String email;
    private String phone;
    private String gender; // male, female, other
    private String avatar;
    private LocalDate birthDate;
    private LocalDate joinDate;
    private Long watchHistory;
    private Long favoritesCount;
    private String vipLevel; // regular, premium, vip
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 用户角色列表（不存储在数据库中，用于权限验证）
    private List<Role> roles;
}
