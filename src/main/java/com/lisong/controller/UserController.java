package com.lisong.controller;

import com.lisong.dto.UserProfileResponse;
import com.lisong.dto.FavoriteRequest;
import com.lisong.dto.FavoriteResponse;
import com.lisong.entity.ApiResponse;
import com.lisong.entity.User;
import com.lisong.entity.Favorite;
import com.lisong.service.UserService;
import com.lisong.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public ApiResponse<UserProfileResponse> getUserProfile(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            UserProfileResponse profile = userService.getUserProfile(userId);
            return ApiResponse.success(profile);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public ApiResponse<User> updateUserProfile(@RequestHeader("Authorization") String authHeader,
                                               @RequestBody User user) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            User updatedUser = userService.updateUserProfile(userId, user);
            return ApiResponse.success(updatedUser);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 收藏/取消收藏
     */
    @PostMapping("/favorites")
    public ApiResponse<FavoriteResponse> manageFavorite(@RequestHeader("Authorization") String authHeader,
                                                        @Valid @RequestBody FavoriteRequest request) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            FavoriteResponse response = userService.manageFavorite(userId, request);
            
            String message = "add".equals(request.getAction()) ? "收藏成功" : "取消收藏成功";
            return new ApiResponse<>(200, message, response);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏列表
     */
    @GetMapping("/favorites")
    public ApiResponse<List<Favorite>> getUserFavorites(@RequestHeader("Authorization") String authHeader,
                                                        @RequestParam(required = false) String contentType,
                                                        @RequestParam(defaultValue = "0") int page,
                                                        @RequestParam(defaultValue = "20") int size) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            List<Favorite> favorites = userService.getUserFavorites(userId, contentType, page, size);
            return ApiResponse.success(favorites);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 检查是否已收藏
     */
    @GetMapping("/favorites/check")
    public ApiResponse<Boolean> checkFavorite(@RequestHeader("Authorization") String authHeader,
                                              @RequestParam Long contentId,
                                              @RequestParam String contentType) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            boolean isFavorited = userService.isFavorited(userId, contentId, contentType);
            return ApiResponse.success(isFavorited);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ApiResponse<String> updatePassword(@RequestHeader("Authorization") String authHeader,
                                              @RequestBody Map<String, String> passwordData) {
        try {
            String token = authHeader.replace("Bearer ", "");
            Long userId = jwtUtil.getUserIdFromToken(token);
            
            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");
            
            if (oldPassword == null || newPassword == null) {
                return ApiResponse.error(400, "旧密码和新密码不能为空");
            }
            
            userService.updatePassword(userId, oldPassword, newPassword);
            return ApiResponse.success("密码修改成功");
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
}
