package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.User;
import com.lisong.entity.Role;
import com.lisong.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {
    
    @Autowired
    private AdminService adminService;
    
    // ========== 用户管理 ==========
    
    /**
     * 获取所有用户列表
     */
    @GetMapping("/users")
    public ApiResponse<Map<String, Object>> getAllUsers(@RequestParam(defaultValue = "0") int page,
                                                        @RequestParam(defaultValue = "20") int size,
                                                        @RequestParam(required = false) String keyword) {
        try {
            List<User> users;
            Long total;
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                users = adminService.searchUsers(keyword.trim(), page, size);
                total = adminService.getSearchUserCount(keyword.trim());
            } else {
                users = adminService.getAllUsers(page, size);
                total = adminService.getUserCount();
            }
            
            Map<String, Object> result = Map.of(
                "users", users,
                "total", total,
                "page", page,
                "size", size,
                "totalPages", (total + size - 1) / size
            );
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }
    
    /**
     * 根据ID获取用户详情
     */
    @GetMapping("/users/{userId}")
    public ApiResponse<User> getUserById(@PathVariable Long userId) {
        try {
            User user = adminService.getUserById(userId);
            if (user == null) {
                return ApiResponse.error(404, "用户不存在");
            }
            return ApiResponse.success(user);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }
    
    /**
     * 创建用户
     */
    @PostMapping("/users")
    public ApiResponse<User> createUser(@RequestBody User user) {
        try {
            User createdUser = adminService.createUser(user);
            return ApiResponse.success(createdUser);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/users/{userId}")
    public ApiResponse<User> updateUser(@PathVariable Long userId, @RequestBody User user) {
        try {
            User updatedUser = adminService.updateUser(userId, user);
            return ApiResponse.success(updatedUser);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/users/{userId}")
    public ApiResponse<String> deleteUser(@PathVariable Long userId) {
        try {
            adminService.deleteUser(userId);
            return ApiResponse.success("用户删除成功");
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 为用户分配角色
     */
    @PostMapping("/users/{userId}/roles/{roleId}")
    public ApiResponse<String> assignRoleToUser(@PathVariable Long userId, @PathVariable Long roleId) {
        try {
            adminService.assignRoleToUser(userId, roleId);
            return ApiResponse.success("角色分配成功");
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 移除用户角色
     */
    @DeleteMapping("/users/{userId}/roles/{roleId}")
    public ApiResponse<String> removeRoleFromUser(@PathVariable Long userId, @PathVariable Long roleId) {
        try {
            adminService.removeRoleFromUser(userId, roleId);
            return ApiResponse.success("角色移除成功");
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // ========== 角色管理 ==========
    
    /**
     * 获取所有角色
     */
    @GetMapping("/roles")
    public ApiResponse<List<Role>> getAllRoles() {
        try {
            List<Role> roles = adminService.getAllRoles();
            return ApiResponse.success(roles);
        } catch (Exception e) {
            return ApiResponse.error(500, e.getMessage());
        }
    }
    
    /**
     * 创建角色
     */
    @PostMapping("/roles")
    public ApiResponse<Role> createRole(@RequestBody Role role) {
        try {
            Role createdRole = adminService.createRole(role);
            return ApiResponse.success(createdRole);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 更新角色
     */
    @PutMapping("/roles/{roleId}")
    public ApiResponse<Role> updateRole(@PathVariable Long roleId, @RequestBody Role role) {
        try {
            Role updatedRole = adminService.updateRole(roleId, role);
            return ApiResponse.success(updatedRole);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 删除角色
     */
    @DeleteMapping("/roles/{roleId}")
    public ApiResponse<String> deleteRole(@PathVariable Long roleId) {
        try {
            adminService.deleteRole(roleId);
            return ApiResponse.success("角色删除成功");
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
}
