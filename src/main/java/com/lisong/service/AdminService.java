package com.lisong.service;

import com.lisong.entity.User;
import com.lisong.entity.Role;
import java.util.List;

/**
 * 管理员服务接口
 */
public interface AdminService {
    
    /**
     * 获取所有用户列表（分页）
     */
    List<User> getAllUsers(int page, int size);
    
    /**
     * 搜索用户
     */
    List<User> searchUsers(String keyword, int page, int size);
    
    /**
     * 获取用户总数
     */
    Long getUserCount();
    
    /**
     * 根据关键词获取用户搜索结果数量
     */
    Long getSearchUserCount(String keyword);
    
    /**
     * 根据ID获取用户详情
     */
    User getUserById(Long userId);
    
    /**
     * 创建用户
     */
    User createUser(User user);
    
    /**
     * 更新用户信息
     */
    User updateUser(Long userId, User user);
    
    /**
     * 删除用户
     */
    void deleteUser(Long userId);
    
    /**
     * 为用户分配角色
     */
    void assignRoleToUser(Long userId, Long roleId);
    
    /**
     * 移除用户角色
     */
    void removeRoleFromUser(Long userId, Long roleId);
    
    /**
     * 获取所有角色
     */
    List<Role> getAllRoles();
    
    /**
     * 创建角色
     */
    Role createRole(Role role);
    
    /**
     * 更新角色
     */
    Role updateRole(Long roleId, Role role);
    
    /**
     * 删除角色
     */
    void deleteRole(Long roleId);
}
