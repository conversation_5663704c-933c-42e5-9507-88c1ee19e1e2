package com.lisong.service;

import com.lisong.dto.UserProfileResponse;
import com.lisong.dto.FavoriteRequest;
import com.lisong.dto.FavoriteResponse;
import com.lisong.entity.User;
import com.lisong.entity.Favorite;
import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 获取用户信息
     */
    UserProfileResponse getUserProfile(Long userId);
    
    /**
     * 更新用户信息
     */
    User updateUserProfile(Long userId, User user);
    
    /**
     * 添加或移除收藏
     */
    FavoriteResponse manageFavorite(Long userId, FavoriteRequest request);
    
    /**
     * 获取用户收藏列表
     */
    List<Favorite> getUserFavorites(Long userId, String contentType, int page, int size);
    
    /**
     * 检查是否已收藏
     */
    boolean isFavorited(Long userId, Long contentId, String contentType);
    
    /**
     * 更新用户密码
     */
    void updatePassword(Long userId, String oldPassword, String newPassword);
}
