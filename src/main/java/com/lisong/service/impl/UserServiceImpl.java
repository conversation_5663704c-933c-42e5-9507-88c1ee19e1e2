package com.lisong.service.impl;

import com.lisong.dto.UserProfileResponse;
import com.lisong.dto.FavoriteRequest;
import com.lisong.dto.FavoriteResponse;
import com.lisong.entity.User;
import com.lisong.entity.Favorite;
import com.lisong.mapper.UserMapper;
import com.lisong.mapper.FavoriteMapper;
import com.lisong.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户服务实现
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private FavoriteMapper favoriteMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public UserProfileResponse getUserProfile(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        return new UserProfileResponse(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getAvatar(),
            user.getJoinDate(),
            user.getWatchHistory(),
            user.getFavoritesCount(),
            user.getVipLevel()
        );
    }
    
    @Override
    @Transactional
    public User updateUserProfile(Long userId, User user) {
        User existingUser = userMapper.findById(userId);
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 更新用户信息
        existingUser.setNickname(user.getNickname());
        existingUser.setEmail(user.getEmail());
        existingUser.setPhone(user.getPhone());
        existingUser.setGender(user.getGender());
        existingUser.setAvatar(user.getAvatar());
        existingUser.setBirthDate(user.getBirthDate());
        
        userMapper.update(existingUser);
        return existingUser;
    }
    
    @Override
    @Transactional
    public FavoriteResponse manageFavorite(Long userId, FavoriteRequest request) {
        // 验证内容类型
        if (!isValidContentType(request.getContentType())) {
            throw new RuntimeException("无效的内容类型");
        }
        
        boolean exists = favoriteMapper.existsByUserIdAndContentIdAndContentType(
            userId, request.getContentId(), request.getContentType()) > 0;
        
        if ("add".equals(request.getAction())) {
            if (exists) {
                throw new RuntimeException("已经收藏过了");
            }
            
            Favorite favorite = new Favorite();
            favorite.setUserId(userId);
            favorite.setContentId(request.getContentId());
            favorite.setContentType(request.getContentType());
            
            favoriteMapper.insert(favorite);
            
            // 更新用户收藏数量
            updateUserFavoriteCount(userId);
            
            return new FavoriteResponse(
                favorite.getId(),
                favorite.getContentId(),
                favorite.getContentType(),
                favorite.getCreatedAt()
            );
            
        } else if ("remove".equals(request.getAction())) {
            if (!exists) {
                throw new RuntimeException("未收藏该内容");
            }
            
            favoriteMapper.deleteByUserIdAndContentIdAndContentType(
                userId, request.getContentId(), request.getContentType());
            
            // 更新用户收藏数量
            updateUserFavoriteCount(userId);
            
            return new FavoriteResponse(null, request.getContentId(), request.getContentType(), null);
            
        } else {
            throw new RuntimeException("无效的操作类型");
        }
    }
    
    @Override
    public List<Favorite> getUserFavorites(Long userId, String contentType, int page, int size) {
        int offset = page * size;
        
        if (contentType != null && !contentType.isEmpty()) {
            return favoriteMapper.findByUserIdAndContentType(userId, contentType, size, offset);
        } else {
            return favoriteMapper.findByUserId(userId, size, offset);
        }
    }
    
    @Override
    public boolean isFavorited(Long userId, Long contentId, String contentType) {
        return favoriteMapper.existsByUserIdAndContentIdAndContentType(userId, contentId, contentType) > 0;
    }
    
    @Override
    @Transactional
    public void updatePassword(Long userId, String oldPassword, String newPassword) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }
        
        // 更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        userMapper.updatePassword(userId, encodedPassword);
    }
    
    /**
     * 验证内容类型是否有效
     */
    private boolean isValidContentType(String contentType) {
        return "movie".equals(contentType) || 
               "tv_series".equals(contentType) || 
               "variety_show".equals(contentType);
    }
    
    /**
     * 更新用户收藏数量
     */
    private void updateUserFavoriteCount(Long userId) {
        Long count = favoriteMapper.countByUserId(userId);
        User user = userMapper.findById(userId);
        if (user != null) {
            user.setFavoritesCount(count);
            userMapper.update(user);
        }
    }
}
