package com.lisong.service.impl;

import com.lisong.entity.User;
import com.lisong.entity.Role;
import com.lisong.entity.UserRole;
import com.lisong.mapper.UserMapper;
import com.lisong.mapper.RoleMapper;
import com.lisong.mapper.UserRoleMapper;
import com.lisong.mapper.FavoriteMapper;
import com.lisong.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 管理员服务实现
 */
@Service
public class AdminServiceImpl implements AdminService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private FavoriteMapper favoriteMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public List<User> getAllUsers(int page, int size) {
        int offset = page * size;
        List<User> users = userMapper.findAllUsers(size, offset);
        
        // 为每个用户加载角色信息
        for (User user : users) {
            user.setRoles(userMapper.findRolesByUserId(user.getId()));
        }
        
        return users;
    }
    
    @Override
    public List<User> searchUsers(String keyword, int page, int size) {
        int offset = page * size;
        List<User> users = userMapper.searchUsers(keyword, size, offset);
        
        // 为每个用户加载角色信息
        for (User user : users) {
            user.setRoles(userMapper.findRolesByUserId(user.getId()));
        }
        
        return users;
    }
    
    @Override
    public Long getUserCount() {
        return userMapper.countUsers();
    }
    
    @Override
    public Long getSearchUserCount(String keyword) {
        return userMapper.countSearchUsers(keyword);
    }
    
    @Override
    public User getUserById(Long userId) {
        User user = userMapper.findById(userId);
        if (user != null) {
            user.setRoles(userMapper.findRolesByUserId(user.getId()));
        }
        return user;
    }
    
    @Override
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (userMapper.findByUsername(user.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (user.getEmail() != null && userMapper.findByEmail(user.getEmail()) != null) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 设置默认值
        if (user.getNickname() == null) {
            user.setNickname(user.getUsername());
        }
        if (user.getJoinDate() == null) {
            user.setJoinDate(LocalDate.now());
        }
        if (user.getWatchHistory() == null) {
            user.setWatchHistory(0L);
        }
        if (user.getFavoritesCount() == null) {
            user.setFavoritesCount(0L);
        }
        if (user.getVipLevel() == null) {
            user.setVipLevel("regular");
        }
        if (user.getStatus() == null) {
            user.setStatus(1);
        }
        
        // 加密密码
        if (user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        
        userMapper.insert(user);
        return user;
    }
    
    @Override
    @Transactional
    public User updateUser(Long userId, User user) {
        User existingUser = userMapper.findById(userId);
        if (existingUser == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 更新用户信息
        existingUser.setNickname(user.getNickname());
        existingUser.setEmail(user.getEmail());
        existingUser.setPhone(user.getPhone());
        existingUser.setGender(user.getGender());
        existingUser.setAvatar(user.getAvatar());
        existingUser.setBirthDate(user.getBirthDate());
        existingUser.setWatchHistory(user.getWatchHistory());
        existingUser.setFavoritesCount(user.getFavoritesCount());
        existingUser.setVipLevel(user.getVipLevel());
        
        userMapper.update(existingUser);
        return existingUser;
    }
    
    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 删除用户的收藏
        favoriteMapper.deleteByUserId(userId);
        
        // 删除用户的角色关联
        userRoleMapper.deleteByUserId(userId);
        
        // 软删除用户
        userMapper.deleteById(userId);
    }
    
    @Override
    @Transactional
    public void assignRoleToUser(Long userId, Long roleId) {
        // 检查用户是否存在
        if (userMapper.findById(userId) == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 检查角色是否存在
        if (roleMapper.findById(roleId) == null) {
            throw new RuntimeException("角色不存在");
        }
        
        // 检查是否已经分配了该角色
        if (userRoleMapper.existsByUserIdAndRoleId(userId, roleId) > 0) {
            throw new RuntimeException("用户已拥有该角色");
        }
        
        UserRole userRole = new UserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        userRoleMapper.insert(userRole);
    }
    
    @Override
    @Transactional
    public void removeRoleFromUser(Long userId, Long roleId) {
        if (userRoleMapper.existsByUserIdAndRoleId(userId, roleId) == 0) {
            throw new RuntimeException("用户未拥有该角色");
        }
        
        userRoleMapper.deleteByUserIdAndRoleId(userId, roleId);
    }
    
    @Override
    public List<Role> getAllRoles() {
        return roleMapper.findAll();
    }
    
    @Override
    @Transactional
    public Role createRole(Role role) {
        // 检查角色名是否已存在
        if (roleMapper.findByName(role.getName()) != null) {
            throw new RuntimeException("角色名已存在");
        }
        
        if (role.getStatus() == null) {
            role.setStatus(1);
        }
        
        roleMapper.insert(role);
        return role;
    }
    
    @Override
    @Transactional
    public Role updateRole(Long roleId, Role role) {
        Role existingRole = roleMapper.findById(roleId);
        if (existingRole == null) {
            throw new RuntimeException("角色不存在");
        }
        
        existingRole.setName(role.getName());
        existingRole.setDescription(role.getDescription());
        
        roleMapper.update(existingRole);
        return existingRole;
    }
    
    @Override
    @Transactional
    public void deleteRole(Long roleId) {
        Role role = roleMapper.findById(roleId);
        if (role == null) {
            throw new RuntimeException("角色不存在");
        }
        
        // 删除所有用户的该角色关联
        List<UserRole> userRoles = userRoleMapper.findByRoleId(roleId);
        for (UserRole userRole : userRoles) {
            userRoleMapper.deleteByUserIdAndRoleId(userRole.getUserId(), roleId);
        }
        
        // 软删除角色
        roleMapper.deleteById(roleId);
    }
}
