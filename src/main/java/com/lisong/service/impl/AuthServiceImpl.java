package com.lisong.service.impl;

import com.lisong.dto.RegisterRequest;
import com.lisong.dto.LoginRequest;
import com.lisong.dto.AuthResponse;
import com.lisong.entity.User;
import com.lisong.entity.Role;
import com.lisong.entity.UserRole;
import com.lisong.mapper.UserMapper;
import com.lisong.mapper.RoleMapper;
import com.lisong.mapper.UserRoleMapper;
import com.lisong.service.AuthService;
import com.lisong.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 认证服务实现
 */
@Service
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    @Transactional
    public AuthResponse register(RegisterRequest request) {
        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("密码确认不匹配");
        }
        
        // 检查用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userMapper.findByEmail(request.getEmail()) != null) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setNickname(request.getUsername()); // 默认昵称为用户名
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setJoinDate(LocalDate.now());
        user.setWatchHistory(0L);
        user.setFavoritesCount(0L);
        user.setVipLevel("regular");
        user.setStatus(1);
        
        // 插入用户
        userMapper.insert(user);
        
        // 分配默认角色（普通用户）
        Role userRole = roleMapper.findByName("USER");
        if (userRole != null) {
            UserRole ur = new UserRole();
            ur.setUserId(user.getId());
            ur.setRoleId(userRole.getId());
            userRoleMapper.insert(ur);
        }
        
        // 获取用户角色
        List<Role> roles = userMapper.findRolesByUserId(user.getId());
        List<String> roleNames = roles.stream().map(Role::getName).collect(Collectors.toList());
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUsername(), user.getId(), roleNames);
        
        return new AuthResponse(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getAvatar(),
            token,
            jwtUtil.getExpirationTime()
        );
    }
    
    @Override
    public AuthResponse login(LoginRequest request) {
        // 查找用户
        User user = userMapper.findByUsername(request.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 获取用户角色
        List<Role> roles = userMapper.findRolesByUserId(user.getId());
        List<String> roleNames = roles.stream().map(Role::getName).collect(Collectors.toList());
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUsername(), user.getId(), roleNames);
        
        return new AuthResponse(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getAvatar(),
            token,
            jwtUtil.getExpirationTime()
        );
    }
    
    @Override
    public void logout(String token) {
        // TODO: 实现令牌黑名单功能
        // 这里可以将令牌添加到黑名单表中
    }
    
    @Override
    public boolean validateToken(String token) {
        return jwtUtil.validateToken(token);
    }
    
    @Override
    public AuthResponse refreshToken(String token) {
        if (!jwtUtil.validateToken(token)) {
            throw new RuntimeException("令牌无效");
        }
        
        String username = jwtUtil.getUsernameFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        List<String> roles = jwtUtil.getRolesFromToken(token);
        
        // 生成新的令牌
        String newToken = jwtUtil.generateToken(username, userId, roles);
        
        User user = userMapper.findById(userId);
        return new AuthResponse(
            userId,
            username,
            user.getEmail(),
            user.getAvatar(),
            newToken,
            jwtUtil.getExpirationTime()
        );
    }
}
