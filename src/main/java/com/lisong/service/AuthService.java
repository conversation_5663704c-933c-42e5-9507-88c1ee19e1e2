package com.lisong.service;

import com.lisong.dto.RegisterRequest;
import com.lisong.dto.LoginRequest;
import com.lisong.dto.AuthResponse;

/**
 * 认证服务接口
 */
public interface AuthService {
    
    /**
     * 用户注册
     */
    AuthResponse register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    AuthResponse login(LoginRequest request);
    
    /**
     * 用户登出
     */
    void logout(String token);
    
    /**
     * 验证令牌是否有效
     */
    boolean validateToken(String token);
    
    /**
     * 刷新令牌
     */
    AuthResponse refreshToken(String token);
}
